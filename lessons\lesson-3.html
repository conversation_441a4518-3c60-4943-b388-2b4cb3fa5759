<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lesson 3: Letter Positions - The Read Qaidah</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#deae35',
                        secondary: '#606060',
                        light: '#F9F7F3',
                        dark: '#101010'
                    },
                    fontFamily: {
                        'arabic': ['Amiri', 'serif'],
                        'sans': ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light font-sans">
    <!-- Header -->
    <header class="bg-dark text-white shadow-lg">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                        <span class="text-dark font-bold text-xl">3</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">The Read Qaidah</h1>
                        <p class="text-sm text-gray-300">Lesson 3: Letter Positions</p>
                    </div>
                </div>
                
                <a href="../index.html" class="bg-primary text-dark px-4 py-2 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                    Lessons
                </a>
            </div>
        </nav>
    </header>

    <main class="container mx-auto px-4 py-8">
        <!-- Lesson Introduction -->
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8 mb-8">
            <div class="text-center mb-6">
                <h2 class="text-3xl font-bold text-dark mb-4">Letter Positions - أوضاع الحروف</h2>
                <p class="text-lg text-secondary mb-4">Understanding how letters change shape based on their position in words</p>
                
                <!-- Progress Bar -->
                <div class="bg-gray-200 rounded-full h-3 mb-4">
                    <div id="lesson-progress-fill" class="bg-primary h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p class="text-sm text-secondary">Progress: <span id="lesson-progress">0/28</span> letters learned</p>
            </div>

            <!-- Learning Modes -->
            <div class="grid md:grid-cols-3 gap-4 mb-6">
                <button id="overview-mode" class="bg-primary text-dark py-3 px-4 rounded-lg font-semibold hover:bg-opacity-90 transition-colors">
                    📊 Overview Mode
                </button>
                <button id="practice-mode" class="bg-gray-200 text-dark py-3 px-4 rounded-lg font-semibold hover:bg-gray-300 transition-colors">
                    🎯 Practice Mode
                </button>
                <button id="quiz-mode" class="bg-gray-200 text-dark py-3 px-4 rounded-lg font-semibold hover:bg-gray-300 transition-colors">
                    🧠 Quiz Mode
                </button>
            </div>
        </div>

        <!-- Position Rules Explanation -->
        <div id="rules-section" class="max-w-5xl mx-auto bg-white rounded-lg shadow-lg p-8 mb-8">
            <div class="text-center mb-8">
                <h3 class="text-3xl font-bold text-dark mb-4">Letter Position Rules</h3>
                <p class="text-lg text-secondary">Most letters change shape when they are at the beginning, middle or end of a word.</p>
            </div>

            <!-- Visual Rules Grid -->
            <div class="grid lg:grid-cols-2 gap-8 mb-8">
                <!-- Left Column: Position Rules -->
                <div class="space-y-6">
                    <!-- Beginning Position -->
                    <div class="bg-light rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mr-4">
                                <span class="text-dark font-bold text-xl font-arabic">بـ</span>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-dark">Letters at the beginning</h4>
                                <p class="text-secondary">of a word have a small connecting line <strong>after</strong> them.</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-center bg-white rounded-lg p-4">
                            <span class="text-4xl font-arabic text-primary font-bold">بـ</span>
                            <div class="w-8 h-1 bg-blue-400 mx-2"></div>
                            <span class="text-sm text-blue-600 font-semibold">connecting line</span>
                        </div>
                    </div>

                    <!-- Middle Position -->
                    <div class="bg-light rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mr-4">
                                <span class="text-dark font-bold text-xl font-arabic">ـبـ</span>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-dark">Letters in the middle</h4>
                                <p class="text-secondary">of a word have a small connecting line <strong>before and after</strong> them.</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-center bg-white rounded-lg p-4">
                            <span class="text-sm text-blue-600 font-semibold">connecting line</span>
                            <div class="w-6 h-1 bg-blue-400 mx-2"></div>
                            <span class="text-4xl font-arabic text-primary font-bold">ـبـ</span>
                            <div class="w-6 h-1 bg-blue-400 mx-2"></div>
                            <span class="text-sm text-blue-600 font-semibold">connecting line</span>
                        </div>
                    </div>

                    <!-- End Position -->
                    <div class="bg-light rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mr-4">
                                <span class="text-dark font-bold text-xl font-arabic">ـب</span>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-dark">Letters at the end</h4>
                                <p class="text-secondary">of a word have a small connecting line <strong>before</strong> them.</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-center bg-white rounded-lg p-4">
                            <span class="text-sm text-blue-600 font-semibold">connecting line</span>
                            <div class="w-8 h-1 bg-blue-400 mx-2"></div>
                            <span class="text-4xl font-arabic text-primary font-bold">ـب</span>
                        </div>
                    </div>
                </div>

                <!-- Right Column: Special Cases -->
                <div class="space-y-6">
                    <!-- Isolated Position -->
                    <div class="bg-light rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl font-arabic">ب</span>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-dark">Isolated Letters</h4>
                                <p class="text-secondary">When a letter stands alone, it has no connecting lines.</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-center bg-white rounded-lg p-4">
                            <span class="text-4xl font-arabic text-secondary font-bold">ب</span>
                        </div>
                    </div>

                    <!-- Non-connecting Letters -->
                    <div class="bg-red-50 border-2 border-red-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mr-4">
                                <span class="text-white font-bold text-xl">!</span>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold text-red-700">Naughty Letters</h4>
                                <p class="text-red-600">Some letters are naughty and don't connect with any letters after them.</p>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg p-4">
                            <p class="text-center text-red-700 font-semibold mb-3">Can you spot these naughty letters?</p>
                            <div class="flex justify-center space-x-4 text-3xl font-arabic font-bold text-red-600">
                                <span>ا</span>
                                <span>د</span>
                                <span>ذ</span>
                                <span>ر</span>
                                <span>ز</span>
                                <span>و</span>
                            </div>
                            <p class="text-center text-sm text-red-600 mt-2">These letters never connect to the letter after them!</p>
                        </div>
                    </div>

                    <!-- Example Word -->
                    <div class="bg-green-50 border-2 border-green-200 rounded-lg p-6">
                        <h4 class="text-xl font-bold text-green-700 mb-4 text-center">Example Word</h4>
                        <div class="bg-white rounded-lg p-4 text-center">
                            <div class="text-5xl font-arabic text-green-700 font-bold mb-2" dir="rtl">كتاب</div>
                            <p class="text-green-600 font-semibold">kitab (book)</p>
                            <div class="mt-4 text-sm text-green-600">
                                <p><span class="font-arabic text-lg">ك</span> - beginning form</p>
                                <p><span class="font-arabic text-lg">ت</span> - middle form</p>
                                <p><span class="font-arabic text-lg">ا</span> - middle form (naughty letter!)</p>
                                <p><span class="font-arabic text-lg">ب</span> - end form</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- Overview Mode: Letter Positions Grid -->
        <div id="overview-content" class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-8 mb-8">
            <div class="text-center mb-6">
                <h3 class="text-2xl font-bold text-dark mb-4">Letter Positions Overview</h3>
                <p class="text-secondary">Click on any letter to see detailed forms and examples</p>
            </div>
            
            <!-- Position Headers -->
            <div class="grid grid-cols-5 gap-2 mb-4 text-center font-semibold text-secondary">
                <div>Letter</div>
                <div>End</div>
                <div>Middle</div>
                <div>Beginning</div>
                <div>Alone</div>
            </div>
            
            <!-- Letters Grid -->
            <div id="letters-shapes-grid" class="space-y-2">
                <!-- Letters will be populated by JavaScript -->
            </div>
        </div>

        <!-- Practice Mode -->
        <div id="practice-content" class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8 mb-8 hidden">
            <div class="text-center mb-6">
                <h3 class="text-2xl font-bold text-dark mb-4">Practice Letter Positions</h3>
                <p class="text-secondary">Learn how each letter changes based on its position in words</p>
            </div>
            
            <!-- Letter Selection -->
            <div id="letter-selector" class="grid grid-cols-7 gap-2 mb-8">
                <!-- Letter buttons will be populated by JavaScript -->
            </div>
            
            <!-- Letter Detail View -->
            <div id="letter-detail" class="hidden">
                <!-- Letter detail content will be populated by JavaScript -->
            </div>
        </div>

        <!-- Quiz Mode -->
        <div id="quiz-content" class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8 mb-8 hidden">
            <div class="text-center mb-6">
                <h3 class="text-2xl font-bold text-dark mb-4">Letter Positions Quiz</h3>
                <p class="text-secondary">Test your knowledge of how letters change based on position</p>
            </div>
            
            <!-- Quiz will be populated by JavaScript -->
            <div id="quiz-container">
                <!-- Quiz content will be populated by JavaScript -->
            </div>
        </div>

        <!-- Lesson Summary -->
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-dark mb-4">Lesson Summary</h3>
            <div class="grid md:grid-cols-2 gap-4 mb-6">
                <div class="text-center p-4 bg-light rounded-lg">
                    <div class="text-2xl font-bold text-primary" id="letters-learned">0</div>
                    <div class="text-sm text-secondary">Letters Learned</div>
                </div>
                <div class="text-center p-4 bg-light rounded-lg">
                    <div class="text-2xl font-bold text-primary" id="completion-percentage">0%</div>
                    <div class="text-sm text-secondary">Completion</div>
                </div>
            </div>

            <div class="text-center">
                <button id="complete-lesson" class="bg-primary text-dark px-8 py-3 rounded-lg hover:bg-opacity-90 transition-colors font-semibold">
                    Complete Lesson
                </button>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-2">May Allah make it easy for you to learn His Book</p>
            <p class="text-sm text-gray-400">The Read Qaidah - Lesson 3: Letter Positions</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../js/lesson-3.js"></script>
</body>
</html>
