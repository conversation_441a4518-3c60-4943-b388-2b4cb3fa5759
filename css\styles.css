/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* CSS Variables */
:root {
    --primary-gold: #deae35;
    --primary-gold-rgb: 222, 174, 53;
    --primary-black: #101010;
    --secondary-gray: #606060;
    --light-cream: #F9F7F3;
    --btn-hover: #e9b93a;
}

/* Custom styles for Arabic text */
.arabic-text {
  font-family: 'Amiri', serif;
  direction: rtl;
  text-align: right;
  line-height: 2;
  font-size: 1.5rem;
}

/* Arabic alphabet grid - RTL layout */
#alphabet-grid {
  direction: rtl;
}

#alphabet-grid > * {
  direction: ltr; /* Reset direction for individual cards */
}

.verse-highlight {
  background-color: rgba(222, 174, 53, 0.2);
  border-radius: 0.375rem;
  padding: 0.5rem;
  transition: all 0.3s ease;
}

.verse-highlight:hover {
  background-color: rgba(222, 174, 53, 0.4);
  cursor: pointer;
}

/* <PERSON> Section Styles */
.hero-section {
    position: relative;
    overflow: hidden;
}

/* Qaidah text glow animation */
.quran-text {
    position: relative;
    transition: text-shadow 0.5s ease;
}

.quran-text.glow {
    text-shadow: 0 0 15px rgba(var(--primary-gold-rgb), 0.8),
                 0 0 30px rgba(var(--primary-gold-rgb), 0.6),
                 0 0 45px rgba(var(--primary-gold-rgb), 0.4);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 1.125rem;
    padding: 0.75rem 2rem;
    border-radius: 0;
    transition: all 0.3s ease;
    text-align: center;
    text-decoration: none;
}

/* Primary button (gold background) */
.btn-primary {
    background-color: var(--primary-gold);
    color: var(--primary-black);
    border: none;
}

.btn-primary:hover {
    background-color: var(--btn-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Secondary button (outlined) */
.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: var(--primary-gold);
    color: var(--primary-black);
    border-color: var(--primary-gold);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Smooth scrolling */
html, body {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

/* Hero content hover effects */
.hero-content:hover .quran-text {
    text-shadow: 0 0 15px rgba(var(--primary-gold-rgb), 0.8),
                 0 0 30px rgba(var(--primary-gold-rgb), 0.6),
                 0 0 45px rgba(var(--primary-gold-rgb), 0.4);
}

/* Audio player custom styles */
.audio-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.play-button {
  background-color: #deae35;
  color: #101010;
  border-radius: 50%;
  padding: 0.75rem;
  transition: all 0.2s ease;
}

.play-button:hover {
  background-color: rgba(222, 174, 53, 0.8);
}

/* Progress bar */
.progress-bar {
  width: 100%;
  background-color: #e5e7eb;
  border-radius: 9999px;
  height: 0.5rem;
}

.progress-fill {
  background-color: #deae35;
  height: 0.5rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .arabic-text {
    font-size: 1.25rem;
    line-height: 1.8;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Hover effects */
.hover-scale:hover {
  transform: scale(1.05);
}

.hover-shadow:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #deae35;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #c49a2e;
}
