// Hero Animation for The Read Qaidah
// <PERSON>vas animation with orbiting Arabic letters inspired by planets

// Initialize hero animation
function initHeroAnimation() {
    // Get required elements
    const quranText = document.getElementById('quran-text');
    const heroContent = document.getElementById('hero-content');
    const canvas = document.getElementById('orbit-canvas');

    // Skip if any required element is missing
    if (!quranText || !heroContent || !canvas) return;

    const ctx = canvas.getContext('2d');

    // Animation state variables
    let animationRunning = false;
    let animationFrameId = null;
    let arabicLetters = [];
    let stars = [];
    let ringOpacity = 0;
    let ringFadeStartTime = 0;
    const ringFadeDuration = 1000;
    let starOpacity = 0;
    let starFadeStartTime = 0;
    const starFadeDuration = 2000;

    // Check if screen is large enough for the animation
    const isLargeScreen = () => window.innerWidth >= 768;

    // Arabic letters for each ring - using Qaidah-related letters
    const innerRingChars = 'قرآ';
    const middleRingChars = 'نيد';
    const outerRingChars = 'ةلا';

    // Set canvas size and initialize stars
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        initStars();
    }

    // Initialize stars for the background
    function initStars() {
        stars = [];
        const numStars = Math.floor(canvas.width * canvas.height / 2000);

        for (let i = 0; i < numStars; i++) {
            stars.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                radius: Math.random() * 2.0 + 0.7,
                opacity: Math.random() * 0.3 + 0.2,
                twinkleSpeed: Math.random() * 0.05 + 0.01,
                twinkleFactor: 0,
                color: Math.random() > 0.7 ? '#deae35' : '#FFFFFF'
            });
        }
    }

    // Initialize the Arabic letters for animation
    function initArabicLetters() {
        arabicLetters = [];

        // Calculate a base radius that's responsive to screen size
        const baseRadius = Math.min(window.innerWidth, window.innerHeight) * 0.25;

        // Define solar system planet data with colors and relative orbital speeds
        const planetData = [
            { color: '#e29468', name: 'Mercury', speed: 0.3072 },
            { color: '#e7cdcd', name: 'Venus', speed: 0.224 },
            { color: '#3d9c4b', name: 'Earth', speed: 0.192 },
            { color: '#c1440e', name: 'Mars', speed: 0.1536 },
            { color: '#e0ae6f', name: 'Jupiter', speed: 0.0832 },
            { color: '#d6b56b', name: 'Saturn', speed: 0.0576 },
            { color: '#b5e3e3', name: 'Uranus', speed: 0.0384 },
            { color: '#5b5ddf', name: 'Neptune', speed: 0.032 }
        ];

        // Define the three rings with their specific letters
        const rings = [
            { chars: innerRingChars, radius: baseRadius * 0.5 },
            { chars: middleRingChars, radius: baseRadius * 0.75 },
            { chars: outerRingChars, radius: baseRadius * 1.0 }
        ];

        // Create letters for each ring
        rings.forEach(ring => {
            for (let i = 0; i < ring.chars.length; i++) {
                // Distribute letters evenly around the ring
                const angle = (i / ring.chars.length) * Math.PI * 2;
                const char = ring.chars.charAt(i);
                const finalRadius = ring.radius;

                // Assign a planet color and speed based on position
                const planetIndex = (rings.indexOf(ring) * ring.chars.length + i) % planetData.length;
                const planet = planetData[planetIndex];

                arabicLetters.push({
                    char: char,
                    angle: angle,
                    radius: finalRadius,
                    speed: planet.speed,
                    planetName: planet.name,
                    size: 14.4, // Reduced size by 20%
                    opacity: 1,
                    ringType: rings.indexOf(ring),
                    planetSize: 16, // Reduced size by 20%
                    planetColor: planet.color
                });
            }
        });
    }

    // Draw the orbiting letters with 3D perspective effect
    function drawOrbitingLetters() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw starry background
        if (starOpacity > 0) {
            ctx.save();
            stars.forEach(star => {
                // Update twinkle effect
                star.twinkleFactor += star.twinkleSpeed;
                const twinkle = Math.sin(star.twinkleFactor) * 0.5 + 0.5;

                ctx.beginPath();
                ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
                ctx.fillStyle = star.color;
                const globalStarBrightness = 0.6;
                ctx.globalAlpha = star.opacity * twinkle * starOpacity * globalStarBrightness;
                ctx.fill();
            });
            ctx.restore();

            // Update star fade-in effect
            if (starOpacity < 1 && starFadeStartTime > 0) {
                const elapsed = Date.now() - starFadeStartTime;
                starOpacity = Math.min(elapsed / starFadeDuration, 1);
            }
        }

        // Calculate responsive ring sizes
        const baseRadius = Math.min(window.innerWidth, window.innerHeight) * 0.25;
        const ringRadii = [baseRadius * 0.5, baseRadius * 0.75, baseRadius * 1.0];

        // 3D perspective parameters
        const tiltAngle = 0.5; // About 29 degrees
        const horizontalStretch = 3.0; // 300% horizontal stretch

        // Draw orbit rings
        if (ringOpacity > 0) {
            ringRadii.forEach(radius => {
                ctx.beginPath();
                ctx.strokeStyle = '#FFFFFF';
                ctx.setLineDash([5, 10]);
                ctx.lineWidth = 2;
                ctx.globalAlpha = 0.15 * ringOpacity;

                // Draw elliptical path
                ctx.beginPath();
                for (let angle = 0; angle < Math.PI * 2; angle += 0.01) {
                    // Calculate position with horizontal stretch
                    const x = Math.cos(angle) * radius * horizontalStretch;
                    const y = Math.sin(angle) * radius;

                    // Apply rotation for tilt
                    const tiltedX = x * Math.cos(-tiltAngle) - y * Math.sin(-tiltAngle);
                    const tiltedY = x * Math.sin(-tiltAngle) + y * Math.cos(-tiltAngle);

                    // Translate to center of canvas
                    const finalX = canvas.width / 2 + tiltedX;
                    const finalY = canvas.height / 2 + tiltedY;

                    if (angle === 0) {
                        ctx.moveTo(finalX, finalY);
                    } else {
                        ctx.lineTo(finalX, finalY);
                    }
                }

                ctx.closePath();
                ctx.stroke();
            });

            // Update ring fade-in effect
            if (ringOpacity < 1 && ringFadeStartTime > 0) {
                const elapsed = Date.now() - ringFadeStartTime;
                ringOpacity = Math.min(elapsed / ringFadeDuration, 1);
            }
        }

        // Draw Arabic letters with planet-like backgrounds
        ctx.setLineDash([]);
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        arabicLetters.forEach(letter => {
            // Update angle (counter-clockwise)
            letter.angle -= letter.speed * 0.01;

            // Calculate position with 3D perspective
            const orbitX = Math.cos(letter.angle) * letter.radius * horizontalStretch;
            const orbitY = Math.sin(letter.angle) * letter.radius;

            // Apply rotation for tilt
            const tiltedX = orbitX * Math.cos(-tiltAngle) - orbitY * Math.sin(-tiltAngle);
            const tiltedY = orbitX * Math.sin(-tiltAngle) + orbitY * Math.cos(-tiltAngle);

            // Final position
            const x = canvas.width / 2 + tiltedX;
            const y = canvas.height / 2 + tiltedY;

            // Draw circular border
            ctx.globalAlpha = letter.opacity;
            ctx.strokeStyle = '#deae35';
            ctx.lineWidth = 1.2; // Reduced by 20%
            ctx.beginPath();
            ctx.arc(x, y, letter.planetSize, 0, Math.PI * 2);
            ctx.stroke();

            // Add planet-colored background
            ctx.fillStyle = letter.planetColor || '#deae35';
            ctx.beginPath();
            ctx.arc(x, y, letter.planetSize, 0, Math.PI * 2);
            ctx.fill();

            // Special effect for Earth (green-blue mix)
            if (letter.planetName === 'Earth') {
                // Add blue ocean patches
                ctx.beginPath();
                ctx.arc(x + letter.planetSize * 0.3, y - letter.planetSize * 0.1, letter.planetSize * 0.5, 0, Math.PI * 0.8);
                ctx.fillStyle = '#4f97dd';
                ctx.fill();

                // Add another ocean patch
                ctx.beginPath();
                ctx.arc(x - letter.planetSize * 0.4, y + letter.planetSize * 0.3, letter.planetSize * 0.4, 0, Math.PI * 1.2);
                ctx.fillStyle = '#4f97dd';
                ctx.fill();
            }

            // Special effect for Saturn (rings)
            if (letter.planetName === 'Saturn') {
                // Draw Saturn's rings
                ctx.beginPath();
                ctx.ellipse(x, y, letter.planetSize * 1.8, letter.planetSize * 0.5, Math.PI / 6, 0, Math.PI * 2);
                ctx.strokeStyle = '#e0ae6f';
                ctx.lineWidth = 2.4; // Reduced by 20%
                ctx.stroke();

                // Draw the planet on top of the rings
                ctx.beginPath();
                ctx.arc(x, y, letter.planetSize, 0, Math.PI * 2);
                ctx.fillStyle = '#d6b56b';
                ctx.fill();
            }

            // Add a subtle highlight for 3D effect
            ctx.beginPath();
            ctx.arc(x - letter.planetSize * 0.3, y - letter.planetSize * 0.3, letter.planetSize * 0.4, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
            ctx.fill();

            // Draw letter inside the circle
            ctx.fillStyle = '#000000';
            ctx.font = `bold ${letter.size}px Amiri`;

            // Add a subtle shadow for better visibility
            ctx.shadowColor = 'rgba(255, 255, 255, 0.7)';
            ctx.shadowBlur = 3;

            // Draw the text twice for a stronger effect
            ctx.fillText(letter.char, x, y);
            ctx.fillText(letter.char, x, y);

            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
        });
    }

    // Animation loop
    function animate() {
        drawOrbitingLetters();
        animationFrameId = requestAnimationFrame(animate);
    }

    // Initialize animation
    canvas.style.opacity = '0';

    if (isLargeScreen()) {
        // Initialize canvas and animation for large screens
        resizeCanvas();
        initArabicLetters();

        // Start animation on hover
        heroContent.addEventListener('mouseenter', function() {
            if (!animationRunning) {
                // Make Quran text glow
                quranText.classList.add('glow');

                // Show canvas with orbiting letters
                canvas.style.opacity = '1';

                // Cancel any existing animation frame
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                    animationFrameId = null;
                }

                // Start animation
                animate();
                animationRunning = true;

                // Start the stars fade-in immediately
                starOpacity = 0.01;
                starFadeStartTime = Date.now();

                // Add a short delay for the rings with fade-in effect
                setTimeout(function() {
                    ringOpacity = 0.01;
                    ringFadeStartTime = Date.now();
                }, 1000);
            }
        });

        // Fade out animation when mouse leaves
        heroContent.addEventListener('mouseleave', function() {
            if (animationRunning) {
                // Fade out animation
                canvas.style.opacity = '0';
                quranText.classList.remove('glow');

                // Reset animation state after fade out
                setTimeout(function() {
                    // Cancel animation frame
                    if (animationFrameId) {
                        cancelAnimationFrame(animationFrameId);
                        animationFrameId = null;
                    }

                    animationRunning = false;

                    // Reset opacities
                    ringOpacity = 0;
                    starOpacity = 0;
                }, 500);
            }
        });
    } else {
        // On smaller screens, hide the canvas completely
        canvas.style.display = 'none';
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (isLargeScreen()) {
            // If screen is large enough, show and update the animation
            canvas.style.display = 'block';
            resizeCanvas();
            initArabicLetters();

            // If animation was running, restart it to adjust to new size
            if (animationRunning) {
                // Cancel existing animation frame
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                    animationFrameId = null;
                }

                // Restart animation
                animate();
            }
        } else {
            // If screen is too small, hide the animation
            canvas.style.display = 'none';
            quranText.classList.remove('glow');

            // Cancel any running animation
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
            animationRunning = false;
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initHeroAnimation);
